# Note: Most of these settings require a restart of envShell,
# 		there are some exceptions, but dock position for example requires a restart

title = "envShell Example Config"
auto-generated = true

[General]
	# If turned off it tries to make most parts of envShell non-transparent, to optimize performance
	# This is usually left on.
	transparency = true
	# With include you can add other config files and load them in order
	include = [
		#"~/.config/envshell/otherfile.toml",
	]

[Display]
	resolution = "1920x1080" # 1920x1080, 2560x1440, 3840x2160

[Window]
	autohide.class."Hyprland" = true
	autohide.class."main.py" = true

	rename.class."code" = "vscode"
	[Window.ignore]
		# This is for pyprland scratchpads
		class."kitty-dropterm" = true
		# This is for the About Menu
		class."main.py" = true
	[Window.translate]
		# Enable this, if you want to manually give each Application a Title
		#force-manual = true

		# Smart title enables following features:
		#	- org.gnome.Nautilus -> Nautilus           : Remove Reverse URL from App class
		#	- visual studio code -> Visual Studio Code : Change to PascalCase
		smart-title = true
		class."None" = "Hyprland"
		class."brave-browser" = "Brave"
		class."org.gnome.Nautilus" = "Finder"
		class."org.gnome.NautilusPreviewer" = "Finder"
		class."org.gnome.Software" = "App Store"
[Notch]
	enable = true
[Notifications]
	enable = true
	[Notifications.Center]
		# This is the position of the notifications
		position = "right" # [left, right]
[ScreenFilter]
	# Implemented effects, include:
	#	- none
	#	- redshift
	#	- nightshift
	enable = true # This enables things like Night and Redshift
	# This is the update interval for the screen filter, in seconds
	# So every 1 second the screen filter will check if it needs to update
	advanced.update_interval = 10 # (seconds)
	# This is the effect that will be used for the screen filter when no rules are applied
	effect = "none"

	# every screen filter has a brightness, temperature and strength
	[ScreenFilter.redshift]
		# Brightness just makes it lighter or darker, 1 is default
		brightness = 1
		# Temperature is the "saturation" or power of the screen filter
		temperature = 10000
		# Strength is like the opacity of the screen filter
		strength = 0.1
	[ScreenFilter.nightshift]
		brightness = 1
		temperature = 10000
		strength = 0.1
	# This is the rules for the screen filter, you can add as many as you want
	#[[ScreenFilter.rules]]
	#	# This is the type of rule, it can be shell or python
	#	# Python might be dangerous, so be careful
	#	type = "shell"
	#	# This is the command that will be run to check if the rule is true or false
	#	# This example checks if the current hour is greater than 17
	#	if = '[ $(date +"%H") -gt 17 ] && echo "true" || echo "false"'
	#	# The output of the command will be compared to this
	#	is = "true"
	#	# If the rule is true, this is the effect that will be used
	#	then = "nightshift"
[Dock]
	enable = false

	# DOES NOT WORK. And will probably take some time to actually get implemented
	magnification = true
	# If you want to use the experimental dock system for hovering (supports Lazy Time)
	experimental = true
	# how long the title of a non-pinned app can be in the dock
	title.limit = 25
	# make the dock push away windows
	exclusive = false
	# time it takes for the dock to start appearing or disappearing
	lazy-time = 1000 # (ms)
	# if you want to make the dock hide
	autohide = true
	# How much space you want between the screen edge and the dock
	# -12 would be touching the screen edge
	gap = 0
	# Makes the dock hide when using hyprlands fullscreen, be it fullscreen 0 or 1.
	# This will hide the dock no matter what settings are set
	hide-on-fullscreen = true
	# if you want to show the dock on an empty workspace
	show-on-workspace = true
	# If you want a dock that expands to the screen edges, or a floating one in the center
	mode = "center" # [center, full]
	# The Height and Icon size of the dock
	size = 1 # 0.1->3
	# What position of the Display
	position = "bottom" # [left, right, bottom]
	# shows the hotspot of the dock
	debug = false
	[Dock.pinned]
		#"app.icon.name" = "command-to-launch-app"
		"org.gnome.Nautilus" = "nautilus"
		"org.gnome.Settings" = "XDG_CURRENT_DESKTOP=GNOME gnome-control-center"
		"org.gnome.Software" = "gnome-software"
		"gnome-disks" = "gnome-disks"
		"org.gnome.baobab" = "baobab"
		"kitty" = "kitty"
		"brave-browser" = "brave"
		"code" = "code"
		"org.gnome.Calculator" = "gnome-calculator"
		"org.gnome.TextEditor" = "gnome-text-editor"
		"org.gnome.Loupe" = "loupe"
		"org.gnome.clocks" = "gnome-clocks"
		"spotify" = "spotify"
[Panel]
	enable = true

	# The height of the panel
	height = 24
	# Whether the panel is full width or not
	full = true
	# If you want a dock that expands to the screen edges, or a floating one in the center
	mode = "normal" # [normal, floating]
	# The date format for the panel
	# %Y Year         = 2025
	# %m Month        = [01,12]
	# %d Day          = [01,31]
	# %H Hour (24)    = [00,23] | %I Hour (12)
	# %M Minute       = [00,59]
	# %S Second       = [00,61]
	# %a abbr weekday = Mon | %A full weekday
	# %b abbr month   = Jan | %B full month
	# %c date & time  = Thu 27 Feb 05:16:23 PM CET 2025
	# %p AM or PM     = PM
	# %z Timezone     = CET
	date-format = "%a %b %d %H:%M"
	# A Nerd Font icon for the panel's left button (commonly referred to as the darwin menu)
	# (here it's called the info-menu)
	icon = ""
	[Panel.Widgets]
		# left Widgets
		left.enable = true
		info-menu.enable = true
		global-title.enable = true
		global-menu.enable = true

		# right Widgets
		right.enable = true
		system-tray.enable = true
		# set this to false, to always have the system tray visible
		system-tray.expandable = true
		battery.enable = true
		wifi.enable = true
		bluetooth.enable = true
		search.enable = true
		control-center.enable = true
		date.enable = true

[EnvLight]
	# This will show the executable below the Name
	show-executable = true
	# This will clear the search bar when you toggle the widget
	clear-search-on-toggle = true
	# This is the maximum length of the executable name
	advanced.executable-max-length = 50
	# This will show the full executable name
	# example:
	# 	nautilus -> nautilus --new-window
	advanced.full-executable = false
	# available builtins:
	# 	"builtins.wikipedia" -> wi prompt
	# 	"builtins.calculator" -> =prompt
	# 	"builtins.duckduckgo" -> ddg prompt
	# You can load extensions like this:
	# Keep in mind that this will overwrite the default extensions, so builtins will be disabled if not added here
	# Also, the extensions will not follow the folder and name structure, but the defined path inside the extension. so not:
	# random_folder/random_extension but [EnvLight.extensions.random_folder.random_extension] would result in random_folder.random_extension
	# so no matter the folder, it will always use the path inside
	load-extensions = [
		"extension://builtins/wikipedia", # This results in a path like this: /home/<USER>/.config/envshell/extensions/builtins/wikipedia
		"extension://builtins/calculator", # Inside this file is the same as below. e.g.: [EnvLight.extensions.builtins.wikipedia]...
		#"extension:///path/to/your/extension", # Would result in an absolute path
		#"extension-folder://builtins", # to load all the builtins
	]
	enabled = [
		"builtins.calculator",
		"builtins.wikipedia"
	]
	# Try changing a keyword to something else
	# like this: EnvLight.extensions.builtins.wikipedia.keyword = "wiki"

	# Every extension has a subgroup
	# [EnvLight.extensions.builtins]
	# and a name
	# [EnvLight.extensions.builtins.wikipedia]

	## Here you can add extensions to envLight
	#[EnvLight.extensions.builtins.wikipedia]
	#	# Every extension has a name
	#	name = "Wikipedia"
	#	# A tooltip for hovering over the extension
	#	tooltip = "Search on Wikipedia."
	#	# The keyword that will be used to search
	#	keyword = "wi"
	#	# The type of the extension, it can be url or command
	#	type = "url"
	#	# The description that will be shown in the envLight
	#	description = "Search for '%s' on Wikipedia"
	#	# The command that will be run when the extension is clicked
	#	command = "https://en.wikipedia.org/w/index.php?search=%s"
	#[EnvLight.extensions.builtins.calculator]
	#	# This is a extension for the calculator
	#	name = "Calculate"
	#	tooltip = "Calculate and Copy to Clipboard."
	#	# It uses python to calculate the expression
	#	keyword = "="
	#	type = "command"
	#	# This will ignore the char limit
	#	ignore-char-limit = true
	#	# This will wrap the description. available modes are: "none", "word", "char"
	#	wrap-mode = "char"
	#	# It also uses an advanced description command to show the result
	#	description-command = "python -c 'print(eval(\"%s\"))'"
	#	# Resulting in an update to the description of the extension
	#	description = "Copy to clipboard %c"
	#	command = "python -c 'print(eval(\"%s\"))' | wl-copy && notify-send \"envShell\" \"Copied to Clipboard\""

[Systray]
	hide-empty = true

[Workspace]
	# Makes the dock ignore apps on workspace id 9
	ignore.id.9 = true

[MusicPlayer]
	# This would ignore any sound control for players with the title being spotify
	#[[MusicPlayer.ignore]]
	#title.regex = "^spotify$"

[Cache]
	# The Directory for cache such as icons
	directory = "envshell"

[Bluetooth]
	# If you want to show Devices without names
	show-hidden-devices = false

[Wifi]
	# When Sentry Mode is enabled, use this as the wifi name
	sentry-mode-wifi = "Anonymous" # for example

[Shell]
# This is rarely changed by the user, you can delete it if you want
	[Shell.about]
		window.width = 250
		window.height = 355
		window.resizable = false
		computer-label = "Env Shell"
		computer-caption = "ESH 0.2.0, 2025"
		more-info = "https://github.com/E3nviction/envshell"
	[Shell.env-menu]
		options.settings.on-click = "code ~/.config/"
		# If you want to use Gnome Control Center, you can uncomment this
		#options.settings.on-click = "XDG_CURRENT_DESKTOP=GNOME gnome-control-center"

		options.store.label = "Nix Store..."
		options.store.on-click = "xdg-open https://search.nixos.org/packages"
		# If you want to use a different Store, such as the Gnome Software store, you can uncomment this
		#options.store.label = "App Store..."
		#options.store.on-click = "gnome-software"

[Mods]
# You can also create mods, which are dropdowns inside the top panel
# You can create as many mods as you want
	# To create a mod, you need to create a section with the name of the mod
	# Then you can add an icon and the icon size (optional. Default: 24)
#	terminal.icon = "/run/current-system/sw/share/icons/WhiteSur-dark/apps/symbolic/terminal-app-symbolic.svg"
#	terminal.icon-size = 18
	# Then you can add options to the mod
	# An option is a label, keybind and an on-click command
	# To create a divider, you can set divider to true, to add a divider below the option
	# 	or just leave the option empty and only add the divider = true, which creates a standalone divider

	# Currently this mod framework only supports list dropdowns, with static labels and on-click commands
	# In the future we plan to add dynamic labels, more option types, and maybe even conditional on-click commands
#	[[Mods.terminal.options]]
#		label = "Terminal"
#		on-clicked = "kitty"
#	[[Mods.terminal.options]]
#		label = "Terminal but scary"
#		on-clicked = "ghostty"
#		# This will add a divider below the last option
#		divider = true

[Misc]
	# This will give you an "Activate Linux" widget at the bottom right, just like in windows
	# This is of course just for the jokes
	activate-linux.enable = false
	# And if you want to change the layer, you can do that here
	# Available layers are: 'background', 'bottom', 'top', 'overlay'
	activate-linux.layer = "background"
