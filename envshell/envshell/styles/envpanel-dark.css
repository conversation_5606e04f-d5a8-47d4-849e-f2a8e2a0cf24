@import "styles/_def.css";
@import "styles/_buttons.css";
@import "styles/_dropdown-dark.css";
@import "styles/_panel.css";
@import "styles/_power.css";
@import "styles/_time.css";
@import "styles/envpanel-dark-about.css";

#global-title-button label {
	font-weight: 600;
}

#system-tray {
	opacity: 1;
	transition: opacity 50ms ease-in-out;
}

#system-tray.hidden {
	opacity: 0;
}

menu {
	background-color: alpha(#000, 0.3);
	box-shadow: inset 0 0 0 1px alpha(#aaa, 0.4);
	border: 1px solid alpha(#111, 0.4);
	border-radius: .75rem;
	padding: .5rem;
}

menu>menuitem {
	background-color: transparent;
	border-radius: 5px;
	padding: 5px 10px;
	margin: 1px 2px;
	transition: all 0ms ease-in-out;
}

menu>menuitem>label {
	font-size: 12px;
	margin: 0px;
	color: #fff;
	font-weight: 400;
}

menu>menuitem:hover {
	transition: all 20ms ease-in-out;
	color: #222;
	background-color: alpha(#2369ff, 1.0);
}

#envshell-osd {
	opacity: 1;
	margin: 5px;
	background-color: alpha(#000, 0.3);
	border: .5px solid alpha(#888, 0.4);
	box-shadow: inset 0 0 200px 0 alpha(#111, 0.3);
	border-radius: 12px;
	padding: 1rem;
	transition: opactiy 250ms ease-in-out;
}