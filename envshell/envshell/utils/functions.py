from config.c import c
from utils.roam import envshell_service
import datetime
import os
import json
import re
import sys
import subprocess
import tomllib
import shutil
from typing import Dict, List, Literal
from fabric import Application

import gi  # type: ignore
from fabric.utils import exec_shell_command, exec_shell_command_async, get_relative_path
from fabric.widgets.image import Image
from gi.repository import Gdk, GLib, Gtk  # type: ignore
from loguru import logger


global envshell_service


def apply_style(app: Application):
    logger.info("[Main] Applying CSS")
    app.set_stylesheet_from_file(get_relative_path("../envshell.css"))
    app.set_stylesheet_from_file(
        os.path.expanduser("~/.config/envshell/style.css"), append=True
    )


def set_socket(value):
    try:
        with open(f"/tmp/envshell.socket", "w") as f:
            # clear file
            f.truncate(0)
            # write value
            f.write(value)
    except Exception as e:
        logger.error("[Main] Failed to create socket:", e)
        sys.exit(1)


# or whatever default makes sense
def get_from_socket(default=["default_mode"]):
    try:
        socket_path = "/tmp/envshell.socket"
        if not os.path.exists(socket_path):
            with open(socket_path, "w") as f:
                f.write("")  # ensure file exists but is empty

        with open(socket_path, "r") as f:
            lines = []
            for line in f:
                line = line.strip()
                if not line:
                    continue
                if line == "false":
                    lines.append(False)
                elif line == "true":
                    lines.append(True)
                else:
                    lines.append(line)

            if not lines:
                return default  # return fallback

            return lines
    except Exception as e:
        logger.error("[Main] Failed to read from socket: {}", e)
        return default


def create_socket_signal(socket: str, name: str, signal: dict):
    try:
        socket_path = os.path.join("/tmp/", socket)

        # Ensure the directory exists
        dir_name = os.path.dirname(socket)
        os.makedirs(os.path.join("/tmp/", dir_name), exist_ok=True)

        # Check if the socket file exists
        if not os.path.exists(socket_path):
            # Initialize the file with empty JSON if it doesn't exist
            with open(socket_path, "w") as f:
                json.dump({}, f)

        # Read the current content of the socket file
        with open(socket_path, "r") as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError:
                # Handle empty or corrupted file (default to empty dictionary)
                data = {}

        # Update the data with the new signal
        data[name] = signal

        # Write the updated data back to the file
        with open(socket_path, "w") as f:
            json.dump(data, f)

    except Exception as e:
        logger.error(f"[Main] Failed to write to socket: {e}")
        # Handle the error as needed; avoid sys.exit(1) unless absolutely necessary
        raise


def get_socket_signal(socket):
    if not os.path.exists(os.path.join("/tmp/", socket)):
        with open(os.path.join("/tmp/", socket), "w") as f:
            f.write("{}")
    with open(os.path.join("/tmp/", socket), "r") as f:
        return json.load(f)


class AppName:
    def __init__(self, path="/usr/share/applications"):
        self.files = os.listdir(path)
        self.path = path

    def get_app_name(self, wmclass, format_=False):
        desktop_file = ""
        for f in self.files:
            if f.startswith(wmclass + ".desktop"):
                desktop_file = f

        desktop_app_name = wmclass

        if desktop_file == "":
            return wmclass
        with open(os.path.join(self.path, desktop_file), "r") as f:
            lines = f.readlines()
            for line in lines:
                if line.startswith("Name="):
                    desktop_app_name = line.split("=")[1].strip()
                    break
        return desktop_app_name

    def get_app_exec(self, wmclass, format_=False):
        desktop_file = ""
        for f in self.files:
            if f.startswith(wmclass + ".desktop"):
                desktop_file = f

        desktop_app_name = wmclass

        if desktop_file == "":
            return wmclass
        with open(os.path.join(self.path, desktop_file), "r") as f:
            lines = f.readlines()
            for line in lines:
                if line.startswith("Exec="):
                    desktop_app_name = line.split("=")[1].strip()
                    break
        return desktop_app_name

    def get_desktop_file(self, wmclass):
        desktop_file = ""
        for f in self.files:
            if f.startswith(wmclass + ".desktop"):
                desktop_file = f
        return desktop_file

    def format_app_name(self, title, wmclass, update=False):
        name = wmclass
        if name == "":
            name = title
        manual = c.get_rule("Window.translate.force-manual")
        smart = c.get_rule("Window.translate.smart-title")
        if c.has_title(wmclass=wmclass):
            name = c.get_title(wmclass=wmclass)
        else:
            name = self.get_app_name(wmclass=wmclass)
        if smart:
            name = str(name).title()
            if "." in name:
                name = name.split(".")[-1]
        if update:
            envshell_service.current_active_app_name = name
        return name


global app_name_class
app_name_class = AppName()
