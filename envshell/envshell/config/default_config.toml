auto-generated = false

[General]
	transparency = true
	include = []

[Display]
	resolution = "1920x1080"

[Window]
	autohide.class."Hyprland" = true
	autohide.class."main.py" = true
	[Window.ignore]
		class."main.py" = true
	[Window.translate]
		smart-title = true
		class."None" = "Hyprland"
[ScreenFilter]
	enable = true
	effect = "none"
	advanced.update_interval = 10
	[ScreenFilter.redshift]
		brightness = 1
		temperature = 10000
		strength = 0.1
	[ScreenFilter.nightshift]
		brightness = 1
		temperature = 10000
		strength = 0.1
[Dock]
	enable = true
	experimental = false
	magnification = true
	title.limit = 25
	exclusive = false
	lazy-time = 1000
	autohide = true
	hide-on-fullscreen = true
	show-on-workspace = true
	mode = "center"
	gap = 0
	size = 1
	position = "bottom"
	debug = false
	[Dock.pinned]
		"org.gnome.Nautilus" = "nautilus"
		"kitty" = "kitty"
[Panel]
	enable = true
	height = 24
	full = true
	autohide = false
	transparent = true
	mode = "normal"
	date-format = "%a %b %d %H:%M"
	icon = ""
	[Panel.Widgets]
		left.enable = true
		info-menu.enable = true
		global-title.enable = true
		global-menu.enable = true

		right.enable = true
		system-tray.enable = true
		system-tray.expandable = true
		battery.enable = true
		wifi.enable = true
		bluetooth.enable = true
		search.enable = true
		control-center.enable = true
		date.enable = true

[Systray]
	hide-empty = true

[Workspace]
	ignore.id.9 = true

[Shell]
	[Shell.about]
		window.width = 250
		window.height = 355
		window.resizable = false
		computer-label = "Env Shell"
		computer-caption = "ESH 0.2.0, 2025"
		more-info = "https://github.com/E3nviction/envshell"
	[Shell.env-menu]
		options.settings.on-click = "code ~/.config/"
		options.store.label = "Nix Store..."
		options.store.on-click = "xdg-open https://search.nixos.org/packages"

[EnvLight]
	show-executable = true
	clear-search-on-toggle = true
	advanced.executable-max-length = 50
	advanced.full-executable = true
	enabled = []
	load-extensions = [
		"extension-folder://builtins",
	]

[Notch]
	enable = true
[Notifications]
	enable = true
	[Notifications.Center]
		position = "right"

[Cache]
	directory = "envshell"

[Bluetooth]
	show-hidden-devices = false

[Wifi]
	sentry-mode-wifi = "Enabled"

[Misc]
	activate-linux.enable = false
	activate-linux.layer = "overlay"